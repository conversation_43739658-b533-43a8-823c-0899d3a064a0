#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
基于事件驱动的启动控制

使用Launch事件系统实现复杂的启动依赖关系。
"""

import os
from pathlib import Path

from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import (AppendEnvironmentVariable, ExecuteProcess, IncludeLaunchDescription,
                            SetEnvironmentVariable, DeclareLaunchArgument, TimerAction,
                            RegisterEventHandler, EmitEvent, LogInfo, OpaqueFunction)
from launch.conditions import IfCondition
from launch.substitutions import LaunchConfiguration
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.event_handlers import OnProcessStart, OnExecutionComplete
from launch_ros.actions import Node, LifecycleNode
from launch_ros.events.lifecycle import ChangeState
from launch_ros.event_handlers import OnStateTransition
from lifecycle_msgs.msg import Transition
from nav2_common.launch import RewrittenYaml


def generate_launch_description():
    # 获取包目录和文件路径
    sim_dir = get_package_share_directory('nav2_minimal_tb3_sim')
    launch_dir = os.path.dirname(os.path.realpath(__file__))

    world_sdf_xacro = os.path.join(sim_dir, 'worlds', 'turtlebot3_house.sdf.xacro')
    robot_sdf = os.path.join(sim_dir, 'urdf', 'gz_waffle_gps.sdf.xacro')
    urdf = os.path.join(sim_dir, 'urdf', 'turtlebot3_waffle_gps.urdf')

    # 读取URDF
    with open(urdf, 'r') as infp:
        robot_description = infp.read()

    # 参数文件配置
    params_file = os.path.join(launch_dir, 'nav2_no_map_params.yaml')
    message_filter_params_file = os.path.join(launch_dir, 'message_filter_params.yaml')
    indoor_ekf_params_file = os.path.join(launch_dir, 'indoor_ekf_params.yaml')
    dual_ekf_params_file = os.path.join(launch_dir, 'dual_ekf_navsat_params.yaml')

    configured_params = RewrittenYaml(source_file=params_file, root_key='', param_rewrites='', convert_types=True)
    configured_message_filter_params = RewrittenYaml(source_file=message_filter_params_file, root_key='', param_rewrites='', convert_types=True)
    configured_indoor_ekf_params = RewrittenYaml(source_file=indoor_ekf_params_file, root_key='', param_rewrites='', convert_types=True)

    # 启动参数
    declare_use_sim_time_cmd = DeclareLaunchArgument('use_sim_time', default_value='True')
    use_sim_time = LaunchConfiguration('use_sim_time')

    # ============================================================================
    # 阶段1: 基础设施层
    # ============================================================================

    # Gazebo仿真器
    gazebo_server = ExecuteProcess(
        cmd=['gz', 'sim', '-r', '-s', world_sdf_xacro],
        output='screen',
        name='gazebo_server'
    )

    # 机器人生成 (当Gazebo启动后)
    robot_spawn = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(os.path.join(sim_dir, 'launch', 'spawn_tb3_gps.launch.py')),
        launch_arguments={
            'use_sim_time': 'True',
            'robot_sdf': robot_sdf,
            'x_pose': '1.17',
            'y_pose': '-1.5',
            'z_pose': '0.01',
        }.items(),
    )

    # 机器人状态发布器
    robot_state_publisher = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{'use_sim_time': True, 'robot_description': robot_description}],
    )

    # ============================================================================
    # 阶段2: 传感器和定位基础
    # ============================================================================

    # Robot Localization包装器
    robot_localization_wrapper = Node(
        package='nav2_system_tests',
        executable='robot_localization_lifecycle_wrapper.py',
        name='robot_localization_lifecycle_wrapper',
        output='screen',
        parameters=[
            {'params_file': dual_ekf_params_file},
            {'use_sim_time': True}
        ],
    )

    # 地图服务器
    map_server = Node(
        package='nav2_map_server',
        executable='map_server',
        name='map_server',
        output='screen',
        parameters=[configured_params, configured_message_filter_params, {'use_sim_time': True}],
        remappings=[('map', 'map'), ('map_metadata', 'map_metadata')],
    )

    # ============================================================================
    # 阶段3: 主要定位
    # ============================================================================

    # AMCL定位
    amcl = Node(
        package='nav2_amcl',
        executable='amcl',
        name='amcl',
        output='screen',
        parameters=[configured_params, configured_message_filter_params, {'use_sim_time': True}],
        remappings=[('scan', 'scan'), ('map', 'map'), ('map_metadata', 'map_metadata')],
    )

    # 室内EKF包装器
    indoor_ekf_wrapper = Node(
        package='nav2_system_tests',
        executable='indoor_ekf_lifecycle_wrapper.py',
        name='indoor_ekf_lifecycle_wrapper',
        output='screen',
        parameters=[
            {'params_file': configured_indoor_ekf_params},
            {'use_sim_time': True}
        ],
    )

    # AMCL和室内EKF的生命周期管理器
    lifecycle_manager_amcl_indoor = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_amcl_indoor',
        output='screen',
        parameters=[
            {'use_sim_time': True},
            {'autostart': False},  # 手动控制启动
            {'node_names': ['amcl', 'indoor_ekf_lifecycle_wrapper']}
        ]
    )

    # Robot Localization的生命周期管理器
    lifecycle_manager_robot_localization = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_robot_localization',
        output='screen',
        parameters=[
            {'use_sim_time': True},
            {'autostart': False},  # 手动控制启动
            {'node_names': ['robot_localization_lifecycle_wrapper']}
        ]
    )

    # Map Server的生命周期管理器
    lifecycle_manager_map_server = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_map_server',
        output='screen',
        parameters=[
            {'use_sim_time': True},
            {'autostart': False},  # 手动控制启动
            {'node_names': ['map_server']}
        ]
    )

    # ============================================================================
    # 事件处理器 - 定义启动顺序
    # ============================================================================

    # 当Gazebo启动后，启动机器人相关组件
    start_robot_after_gazebo = RegisterEventHandler(
        OnProcessStart(
            target_action=gazebo_server,
            on_start=[
                LogInfo(msg="Gazebo started, launching robot components..."),
                TimerAction(
                    period=2.0,  # 等待2秒让Gazebo稳定
                    actions=[robot_spawn, robot_state_publisher]
                )
            ]
        )
    )

    # 当机器人生成后，同时启动所有定位组件（但不激活）
    start_localization_components_after_robot = RegisterEventHandler(
        OnExecutionComplete(
            target_action=robot_spawn,
            on_completion=[
                LogInfo(msg="Robot spawned, starting all localization components..."),
                TimerAction(
                    period=3.0,  # 等待传感器数据稳定
                    actions=[
                        # 启动所有定位相关节点
                        robot_localization_wrapper,
                        map_server,
                        amcl,
                        indoor_ekf_wrapper,
                        # 启动三个独立的生命周期管理器
                        lifecycle_manager_amcl_indoor,      # 管理AMCL和室内EKF
                        lifecycle_manager_robot_localization, # 管理Robot Localization
                        lifecycle_manager_map_server,       # 管理Map Server
                        # 只配置但不激活生命周期管理器
                        TimerAction(
                            period=2.0,
                            actions=[
                                LogInfo(msg="Configuring all lifecycle managers (not activating)..."),
                                # 配置AMCL和室内EKF管理器
                                EmitEvent(event=ChangeState(
                                    lifecycle_node_matcher=lambda node: node.node_name == 'lifecycle_manager_amcl_indoor',
                                    transition_id=Transition.TRANSITION_CONFIGURE
                                )),
                                # 配置Robot Localization管理器
                                EmitEvent(event=ChangeState(
                                    lifecycle_node_matcher=lambda node: node.node_name == 'lifecycle_manager_robot_localization',
                                    transition_id=Transition.TRANSITION_CONFIGURE
                                )),
                                # 配置Map Server管理器
                                EmitEvent(event=ChangeState(
                                    lifecycle_node_matcher=lambda node: node.node_name == 'lifecycle_manager_map_server',
                                    transition_id=Transition.TRANSITION_CONFIGURE
                                )),
                            ]
                        )
                    ]
                )
            ]
        )
    )

    # ============================================================================
    # 激活控制器 - 三个独立的生命周期管理器
    # ============================================================================

    # 选项1：基于事件的智能激活（推荐）

    # 当Map Server配置完成后激活
    activate_map_server_on_configured = RegisterEventHandler(
        OnStateTransition(
            target_lifecycle_node='lifecycle_manager_map_server',
            start_state='configuring',
            goal_state='inactive',
            entities=[
                LogInfo(msg="Map Server configured, activating..."),
                TimerAction(
                    period=1.0,  # 短暂延迟确保状态稳定
                    actions=[
                        EmitEvent(event=ChangeState(
                            lifecycle_node_matcher=lambda node: node.node_name == 'lifecycle_manager_map_server',
                            transition_id=Transition.TRANSITION_ACTIVATE
                        ))
                    ]
                )
            ]
        )
    )

    # 当Map Server激活后，激活Robot Localization
    activate_robot_localization_after_map = RegisterEventHandler(
        OnStateTransition(
            target_lifecycle_node='lifecycle_manager_map_server',
            start_state='activating',
            goal_state='active',
            entities=[
                LogInfo(msg="Map Server active, activating Robot Localization..."),
                TimerAction(
                    period=2.0,  # 等待地图服务稳定
                    actions=[
                        EmitEvent(event=ChangeState(
                            lifecycle_node_matcher=lambda node: node.node_name == 'lifecycle_manager_robot_localization',
                            transition_id=Transition.TRANSITION_ACTIVATE
                        ))
                    ]
                )
            ]
        )
    )

    # 当Robot Localization激活后，激活AMCL和室内EKF
    activate_amcl_indoor_after_robot_loc = RegisterEventHandler(
        OnStateTransition(
            target_lifecycle_node='lifecycle_manager_robot_localization',
            start_state='activating',
            goal_state='active',
            entities=[
                LogInfo(msg="Robot Localization active, activating AMCL and Indoor EKF..."),
                TimerAction(
                    period=2.0,  # 等待GPS定位稳定
                    actions=[
                        EmitEvent(event=ChangeState(
                            lifecycle_node_matcher=lambda node: node.node_name == 'lifecycle_manager_amcl_indoor',
                            transition_id=Transition.TRANSITION_ACTIVATE
                        ))
                    ]
                )
            ]
        )
    )

    # 选项2：简单延时激活（备用方案）
    # 如果事件驱动有问题，可以使用这些简单的延时激活器

    # activate_map_server_timer = TimerAction(
    #     period=15.0,  # 15秒后激活地图服务
    #     actions=[
    #         LogInfo(msg="Timer-based: Auto-activating Map Server..."),
    #         EmitEvent(event=ChangeState(
    #             lifecycle_node_matcher=lambda node: node.node_name == 'lifecycle_manager_map_server',
    #             transition_id=Transition.TRANSITION_ACTIVATE
    #         ))
    #     ]
    # )

    # activate_robot_localization_timer = TimerAction(
    #     period=18.0,  # 18秒后激活GPS定位
    #     actions=[
    #         LogInfo(msg="Timer-based: Auto-activating Robot Localization..."),
    #         EmitEvent(event=ChangeState(
    #             lifecycle_node_matcher=lambda node: node.node_name == 'lifecycle_manager_robot_localization',
    #             transition_id=Transition.TRANSITION_ACTIVATE
    #         ))
    #     ]
    # )

    # activate_amcl_indoor_timer = TimerAction(
    #     period=20.0,  # 20秒后激活AMCL和室内EKF
    #     actions=[
    #         LogInfo(msg="Timer-based: Auto-activating AMCL and Indoor EKF..."),
    #         EmitEvent(event=ChangeState(
    #             lifecycle_node_matcher=lambda node: node.node_name == 'lifecycle_manager_amcl_indoor',
    #             transition_id=Transition.TRANSITION_ACTIVATE
    #         ))
    #     ]
    # )

    # 选项3：基于条件的智能激活（高级用法）
    # 可以基于话题数据、服务可用性等条件来激活

    # 示例：基于传感器数据可用性激活
    # activate_when_sensors_ready = OpaqueFunction(
    #     function=lambda context: [
    #         # 检查传感器话题是否有数据
    #         # 这里可以添加自定义逻辑来检查GPS信号、激光雷达数据等
    #         LogInfo(msg="Checking sensor readiness..."),
    #         # 根据条件决定激活哪些组件
    #     ]
    # )

    # 选项4：完全手动控制
    # 如果要完全手动控制，请注释掉所有自动激活器，并使用以下命令：
    #
    # 激活Map Server：
    # ros2 lifecycle set /lifecycle_manager_map_server activate
    #
    # 激活Robot Localization：
    # ros2 lifecycle set /lifecycle_manager_robot_localization activate
    #
    # 激活AMCL和室内EKF：
    # ros2 lifecycle set /lifecycle_manager_amcl_indoor activate
    #
    # 停用对应系统：
    # ros2 lifecycle set /lifecycle_manager_map_server deactivate
    # ros2 lifecycle set /lifecycle_manager_robot_localization deactivate
    # ros2 lifecycle set /lifecycle_manager_amcl_indoor deactivate
    #
    # 查看状态：
    # ros2 lifecycle list
    # ros2 lifecycle get /lifecycle_manager_map_server
    # ros2 lifecycle get /lifecycle_manager_robot_localization
    # ros2 lifecycle get /lifecycle_manager_amcl_indoor

    # ============================================================================
    # 返回Launch描述
    # ============================================================================

    return LaunchDescription([
        # 参数声明
        declare_use_sim_time_cmd,

        # 环境变量设置
        SetEnvironmentVariable('RCUTILS_LOGGING_BUFFERED_STREAM', '1'),
        SetEnvironmentVariable('RCUTILS_LOGGING_USE_STDOUT', '1'),
        AppendEnvironmentVariable('GZ_SIM_RESOURCE_PATH', os.path.join(sim_dir, 'models')),
        AppendEnvironmentVariable('GZ_SIM_RESOURCE_PATH', str(Path(os.path.join(sim_dir)).parent.resolve())),

        # 基础设施
        gazebo_server,

        # 事件处理器
        start_robot_after_gazebo,
        start_localization_components_after_robot,

        # 基于事件的智能激活控制器（推荐使用）
        activate_map_server_on_configured,      # 配置完成后激活地图服务
        activate_robot_localization_after_map,  # 地图激活后激活GPS/IMU融合
        activate_amcl_indoor_after_robot_loc,   # GPS激活后激活AMCL和室内EKF

        # 如果需要使用延时激活，请注释掉上面的事件驱动激活器，并取消注释下面的：
        # activate_map_server_timer,
        # activate_robot_localization_timer,
        # activate_amcl_indoor_timer,
    ])


if __name__ == '__main__':
    generate_launch_description()
