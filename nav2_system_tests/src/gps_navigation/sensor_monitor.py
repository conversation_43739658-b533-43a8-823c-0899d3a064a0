#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
传感器监控节点

持续监控关键传感器的状态，只有当所有必需传感器都可用时，
才发布传感器就绪事件，触发生命周期管理器的配置。
"""

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, DurabilityPolicy
from std_msgs.msg import Bool, String
from sensor_msgs.msg import LaserScan, Imu
from nav_msgs.msg import Odometry
from sensor_msgs.msg import NavSatFix
import threading
import time


class SensorMonitor(Node):
    def __init__(self):
        super().__init__('sensor_monitor')
        
        # 参数
        self.declare_parameter('required_sensors', ['scan', 'imu', 'gps', 'odom'])
        self.declare_parameter('check_frequency', 2.0)
        self.declare_parameter('timeout_per_sensor', 3.0)
        
        self.required_sensors = self.get_parameter('required_sensors').value
        self.check_frequency = self.get_parameter('check_frequency').value
        self.timeout_per_sensor = self.get_parameter('timeout_per_sensor').value
        
        # 传感器状态跟踪
        self.sensor_status = {sensor: False for sensor in self.required_sensors}
        self.last_received = {sensor: None for sensor in self.required_sensors}
        
        # QoS配置
        qos_profile = QoSProfile(
            reliability=ReliabilityPolicy.BEST_EFFORT,
            durability=DurabilityPolicy.VOLATILE,
            depth=1
        )
        
        # 订阅传感器话题
        self.subscribers = {}
        if 'scan' in self.required_sensors:
            self.subscribers['scan'] = self.create_subscription(
                LaserScan, '/scan', 
                lambda msg: self.sensor_callback('scan', msg), qos_profile)
                
        if 'imu' in self.required_sensors:
            self.subscribers['imu'] = self.create_subscription(
                Imu, '/imu', 
                lambda msg: self.sensor_callback('imu', msg), qos_profile)
                
        if 'gps' in self.required_sensors:
            self.subscribers['gps'] = self.create_subscription(
                NavSatFix, '/gps/fix', 
                lambda msg: self.sensor_callback('gps', msg), qos_profile)
                
        if 'odom' in self.required_sensors:
            self.subscribers['odom'] = self.create_subscription(
                Odometry, '/odom', 
                lambda msg: self.sensor_callback('odom', msg), qos_profile)
        
        # 发布传感器状态
        self.status_publisher = self.create_publisher(Bool, '/sensors_ready', 10)
        self.detail_publisher = self.create_publisher(String, '/sensor_status_detail', 10)
        
        # 状态标志
        self.all_sensors_ready = False
        self.ready_event_published = False
        
        # 定时器 - 定期检查传感器状态
        self.timer = self.create_timer(1.0 / self.check_frequency, self.check_sensors)
        
        self.get_logger().info(f'Sensor monitor started. Required sensors: {self.required_sensors}')
        self.get_logger().info(f'Check frequency: {self.check_frequency} Hz')
        self.get_logger().info(f'Timeout per sensor: {self.timeout_per_sensor} seconds')

    def sensor_callback(self, sensor_name, msg):
        """传感器数据回调"""
        current_time = self.get_clock().now()
        self.last_received[sensor_name] = current_time
        
        # 检查数据有效性
        is_valid = self.validate_sensor_data(sensor_name, msg)
        
        if is_valid and not self.sensor_status[sensor_name]:
            self.sensor_status[sensor_name] = True
            self.get_logger().info(f'Sensor {sensor_name} is now READY')
        elif not is_valid and self.sensor_status[sensor_name]:
            self.sensor_status[sensor_name] = False
            self.get_logger().warn(f'Sensor {sensor_name} data is INVALID')

    def validate_sensor_data(self, sensor_name, msg):
        """验证传感器数据有效性"""
        try:
            if sensor_name == 'scan':
                # 检查激光雷达数据
                return len(msg.ranges) > 0 and any(r > msg.range_min and r < msg.range_max for r in msg.ranges)
            elif sensor_name == 'imu':
                # 检查IMU数据
                return (msg.orientation_covariance[0] >= 0 and 
                       msg.angular_velocity_covariance[0] >= 0 and
                       msg.linear_acceleration_covariance[0] >= 0)
            elif sensor_name == 'gps':
                # 检查GPS数据
                return (msg.status.status >= 0 and 
                       abs(msg.latitude) > 0.001 and 
                       abs(msg.longitude) > 0.001)
            elif sensor_name == 'odom':
                # 检查里程计数据
                return msg.pose.covariance[0] >= 0
            else:
                return True
        except Exception as e:
            self.get_logger().error(f'Error validating {sensor_name} data: {e}')
            return False

    def check_sensors(self):
        """定期检查传感器状态"""
        current_time = self.get_clock().now()
        
        # 检查每个传感器的超时状态
        for sensor_name in self.required_sensors:
            if self.last_received[sensor_name] is None:
                # 从未接收到数据
                self.sensor_status[sensor_name] = False
            else:
                # 检查是否超时
                time_diff = (current_time - self.last_received[sensor_name]).nanoseconds / 1e9
                if time_diff > self.timeout_per_sensor:
                    if self.sensor_status[sensor_name]:
                        self.get_logger().warn(f'Sensor {sensor_name} TIMEOUT (last seen {time_diff:.1f}s ago)')
                    self.sensor_status[sensor_name] = False
        
        # 检查是否所有传感器都就绪
        all_ready = all(self.sensor_status.values())
        
        if all_ready and not self.all_sensors_ready:
            # 所有传感器刚刚变为就绪状态
            self.all_sensors_ready = True
            self.get_logger().info('🎉 ALL SENSORS ARE READY! Publishing ready event...')
            self.publish_ready_event()
            
        elif not all_ready and self.all_sensors_ready:
            # 有传感器失效
            self.all_sensors_ready = False
            self.ready_event_published = False
            not_ready = [name for name, status in self.sensor_status.items() if not status]
            self.get_logger().warn(f'Sensors not ready: {not_ready}')
        
        # 发布状态信息
        self.publish_status()

    def publish_ready_event(self):
        """发布传感器就绪事件"""
        if not self.ready_event_published:
            # 发布布尔状态
            ready_msg = Bool()
            ready_msg.data = True
            self.status_publisher.publish(ready_msg)
            
            # 发布详细状态
            detail_msg = String()
            detail_msg.data = "ALL_SENSORS_READY"
            self.detail_publisher.publish(detail_msg)
            
            self.ready_event_published = True
            self.get_logger().info('Sensor ready event published!')

    def publish_status(self):
        """发布当前传感器状态"""
        # 发布布尔状态
        ready_msg = Bool()
        ready_msg.data = self.all_sensors_ready
        self.status_publisher.publish(ready_msg)
        
        # 发布详细状态
        detail_msg = String()
        status_list = [f"{name}:{'✓' if status else '✗'}" for name, status in self.sensor_status.items()]
        detail_msg.data = f"[{', '.join(status_list)}]"
        self.detail_publisher.publish(detail_msg)


def main(args=None):
    rclpy.init(args=args)
    
    sensor_monitor = SensorMonitor()
    
    try:
        rclpy.spin(sensor_monitor)
    except KeyboardInterrupt:
        pass
    finally:
        sensor_monitor.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
