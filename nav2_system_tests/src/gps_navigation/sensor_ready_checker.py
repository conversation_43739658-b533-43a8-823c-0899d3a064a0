#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
传感器就绪检查器节点

监听传感器监控节点发布的传感器就绪状态，
当所有传感器就绪时，通过服务调用配置生命周期管理器。
"""

import rclpy
from rclpy.node import Node
from std_msgs.msg import Bool
from lifecycle_msgs.srv import ChangeState
from lifecycle_msgs.msg import Transition
import threading
import time


class SensorReadyChecker(Node):
    def __init__(self):
        super().__init__('sensor_ready_checker')
        
        # 参数
        self.declare_parameter('sensor_ready_topic', '/sensors_ready')
        self.sensor_ready_topic = self.get_parameter('sensor_ready_topic').value
        
        # 状态跟踪
        self.sensors_ready = False
        self.managers_configured = False
        
        # 生命周期管理器列表
        self.lifecycle_managers = [
            'lifecycle_manager_map_server',
            'lifecycle_manager_robot_localization',
            'lifecycle_manager_amcl_indoor'
        ]
        
        # 订阅传感器就绪状态
        self.subscription = self.create_subscription(
            Bool, 
            self.sensor_ready_topic, 
            self.sensor_ready_callback, 
            10
        )
        
        # 创建生命周期服务客户端
        self.lifecycle_clients = {}
        for manager in self.lifecycle_managers:
            service_name = f'/{manager}/change_state'
            self.lifecycle_clients[manager] = self.create_client(
                ChangeState, service_name
            )
        
        self.get_logger().info(f'Sensor ready checker started')
        self.get_logger().info(f'Listening to: {self.sensor_ready_topic}')
        self.get_logger().info(f'Will configure managers: {self.lifecycle_managers}')

    def sensor_ready_callback(self, msg):
        """传感器就绪状态回调"""
        if msg.data and not self.sensors_ready:
            # 传感器刚刚变为就绪状态
            self.sensors_ready = True
            self.get_logger().info('🎉 Sensors are ready! Starting lifecycle manager configuration...')
            
            # 在单独线程中配置管理器，避免阻塞回调
            threading.Thread(target=self.configure_lifecycle_managers, daemon=True).start()
            
        elif not msg.data and self.sensors_ready:
            # 传感器失效
            self.sensors_ready = False
            self.get_logger().warn('⚠️ Sensors are no longer ready!')

    def configure_lifecycle_managers(self):
        """配置所有生命周期管理器"""
        if self.managers_configured:
            self.get_logger().info('Lifecycle managers already configured, skipping...')
            return
        
        self.get_logger().info('Configuring lifecycle managers...')
        
        # 等待所有服务可用
        for manager, client in self.lifecycle_clients.items():
            self.get_logger().info(f'Waiting for service: /{manager}/change_state')
            if not client.wait_for_service(timeout_sec=10.0):
                self.get_logger().error(f'Service /{manager}/change_state not available!')
                return
        
        # 配置所有管理器
        success_count = 0
        for manager, client in self.lifecycle_clients.items():
            if self.configure_manager(manager, client):
                success_count += 1
            else:
                self.get_logger().error(f'Failed to configure {manager}')
        
        if success_count == len(self.lifecycle_managers):
            self.managers_configured = True
            self.get_logger().info(f'✅ Successfully configured all {success_count} lifecycle managers!')
            self.get_logger().info('🚀 System is ready for activation!')
        else:
            self.get_logger().error(f'❌ Only {success_count}/{len(self.lifecycle_managers)} managers configured successfully')

    def configure_manager(self, manager_name, client):
        """配置单个生命周期管理器"""
        try:
            # 创建配置请求
            request = ChangeState.Request()
            request.transition = Transition()
            request.transition.id = Transition.TRANSITION_CONFIGURE
            request.transition.label = 'configure'
            
            self.get_logger().info(f'Configuring {manager_name}...')
            
            # 发送同步请求
            future = client.call_async(request)
            rclpy.spin_until_future_complete(self, future, timeout_sec=5.0)
            
            if future.result() is not None:
                response = future.result()
                if response.success:
                    self.get_logger().info(f'✅ {manager_name} configured successfully')
                    return True
                else:
                    self.get_logger().error(f'❌ {manager_name} configuration failed')
                    return False
            else:
                self.get_logger().error(f'❌ {manager_name} configuration service call failed')
                return False
                
        except Exception as e:
            self.get_logger().error(f'❌ Exception configuring {manager_name}: {e}')
            return False

    def get_manager_status(self):
        """获取管理器状态（用于调试）"""
        status = {
            'sensors_ready': self.sensors_ready,
            'managers_configured': self.managers_configured,
            'lifecycle_managers': self.lifecycle_managers
        }
        return status


def main(args=None):
    rclpy.init(args=args)
    
    sensor_ready_checker = SensorReadyChecker()
    
    try:
        # 使用多线程执行器以支持服务调用
        from rclpy.executors import MultiThreadedExecutor
        executor = MultiThreadedExecutor()
        executor.add_node(sensor_ready_checker)
        
        sensor_ready_checker.get_logger().info('Sensor ready checker is running...')
        executor.spin()
        
    except KeyboardInterrupt:
        sensor_ready_checker.get_logger().info('Sensor ready checker shutting down...')
    finally:
        sensor_ready_checker.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
